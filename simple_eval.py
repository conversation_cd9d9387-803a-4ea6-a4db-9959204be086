from braintrust import Eva<PERSON>
from autoevals import <PERSON>enshtein
from braintrust import init_logger, traced

# Initialize Braintrust logger
logger = init_logger(project="pedro-project1")

# Your evaluation code
Eval(
    "pedro-project1",
    data=lambda: [
        {
            "input": "Foo",
            "expected": "Hi Foo",
        },
        {
            "input": "Bar",
            "expected": "Hello Bar",
        },
    ],
    task=lambda input: "Hi " + input,
    scores=[Levenshtein],
)