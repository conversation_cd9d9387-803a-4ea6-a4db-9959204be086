import os
from typing import List, Dict, Any, Optional
 
import requests
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util.retry import Retry
 
from datasets import load_dataset
 
import braintrust
import autoevals
 
from twelvelabs import TwelveLabs
 
 
RETRY_TOTAL = 3
RETRY_BACKOFF = 0.5
STATUS_FORCELIST = [502, 503, 504]

bt_api_key='sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU'
twlabs_api_key='tlk_2X6XARP1DDRGS022R26W22TMBQFC'

# Initialize Braintrust
BRAINTRUST_API_KEY = bt_api_key
 
if not BRAINTRUST_API_KEY:
    raise ValueError("Please set the BRAINTRUST_API_KEY environment variable.")
 
# Initialize Twelve Labs
TWELVE_LABS_API_KEY = twlabs_api_key
 
if not TWELVE_LABS_API_KEY:
    raise ValueError("Please set the TWELVE_LABS_API_KEY environment variable.")
 
twelvelabs_client = TwelveLabs(api_key=TWELVE_LABS_API_KEY)

def get_video_data(video_path: str, session: requests.Session) -> Optional[bytes]:
    try:
        if video_path.startswith("http"):
            response = session.get(video_path, timeout=10)
            response.raise_for_status()
            return response.content
        else:
            with open(video_path, "rb") as f:
                return f.read()
    except Exception as e:
        print(f"Error retrieving video data from {video_path}: {e}")
        return None
    
# Create or retrieve pegasus index
models = [{"name": "pegasus1.2", "options": ["visual", "audio"]}]
 
index_name = "mmvu_videos"
indices_list = twelvelabs_client.index.list(name=index_name)
 
if len(indices_list) == 0:
    index = twelvelabs_client.index.create(
        name=index_name, models=models, addons=["thumbnail"]
    )
    print(
        f"A new index has been created: id={index.id} name={index.name} models={index.models}"
    )
else:
    index = indices_list[0]
    print(
        f"Index already exists: id={index.id} name={index.name} models={index.models}"
    )

def on_task_update(task):
    print(f"  Status={task.status}")
 
 
def upload_video_to_twelve_labs(index, video_url):

    if video_url == 'https://huggingface.co/datasets/yale-nlp/MMVU/resolve/main/videos/Electrical_Engineering/0.mp4' \
        or video_url == 'https://huggingface.co/datasets/yale-nlp/MMVU/resolve/main/videos/Electrical_Engineering/1.mp4' \
        or video_url == 'https://huggingface.co/datasets/yale-nlp/MMVU/resolve/main/videos/Materials_Science/0.mp4':
        pass
    else:
        task = twelvelabs_client.task.create(index_id=index.id, url=video_url)
        print(f"Task created: id={task.id} status={task.status}")
 
        task.wait_for_done(sleep_interval=5, callback=on_task_update)
 
        if task.status != "ready":
            raise RuntimeError(f"Indexing failed with status {task.status}")
        print(f"The unique identifier of your video is {task.video_id}.")
 
        # return the video id
        return task.video_id

video_id_dict = {}

def load_data_subset() -> List[Dict[str, Any]]:
    ds = load_dataset("yale-nlp/MMVU", split="validation[:20]")
 
    session = requests.Session()
    retry = Retry(
        total=RETRY_TOTAL,
        backoff_factor=RETRY_BACKOFF,
        status_forcelist=STATUS_FORCELIST,
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
 
    data_list = []
 
    for row in ds:
        question_type = row["question_type"]
        video_path = row["video"]
        print(row["video"])
 
        raw_video = get_video_data(video_path, session)
 
        choices_data = (
            row.get("choices") if question_type == "multiple-choice" else None
        )
 
        if video_path in video_id_dict.keys():
            video_id = video_id_dict[video_path]
        else:
            video_id = upload_video_to_twelve_labs(index, video_path)
            video_id_dict[video_path] = video_id
 
        data_list.append(
            {
                "input": {
                    "video_id": video_id,
                    "question": row["question"],
                    "question_type": question_type,
                    "choices": choices_data,
                    "video_attachment": braintrust.Attachment(
                        filename=os.path.basename(video_path),
                        content_type="video/mp4",
                        data=raw_video,
                    ),
                },
                "expected": {"answer": row["answer"]},
                "metadata": {
                    "subject": row["metadata"]["subject"],
                    "textbook": row["metadata"]["textbook"],
                    "question_type": question_type,
                },
            }
        )
 
    session.close()
    return data_list

load_data_subset()

def video_qa(input_dict: Dict[str, Any]) -> str:
    video_id = input_dict["video_id"]
    question = input_dict["question"]
    question_type = input_dict.get("question_type", "open-ended")
    choices_data = input_dict.get("choices")
 
    if question_type == "multiple-choice" and choices_data:
        if isinstance(choices_data, dict):
            options_text = "\n".join(
                f"{key}: {value}" for key, value in choices_data.items()
            )
        else:
            options_text = "\n".join(
                f"{chr(65 + i)}: {option}" for i, option in enumerate(choices_data)
            )
        prompt_text = (
            f"answer the following question: {question}.\n\n"
            f"Here are your options:\n{options_text}\n"
            "Choose the correct option in the format 'answer: X' where X is the letter that corresponds to the correct choice. If uncertain, guess. You MUST pick something."
        )
    else:
        prompt_text = (
            f"Answer the following question: {question}. Use the most succinct language possible.\n"
            "If uncertain, guess. Provide the best possible answer. You MUST answer to the best of your ability."
        )
 
    res = twelvelabs_client.generate.text(video_id=video_id, prompt=prompt_text)
    return res.data

evaluator = autoevals.LLMClassifier(
    name="evaluator",
    prompt_template=(
        "You are a judge evaluating a model's ability to answer a question "
        "Model's answer:\n{{output}}\n\n"
        "Expected answer:\n{{expected.answer}}\n\n"
        "Is the model's answer correct? (Y/N)? Only Y or N."
    ),
    choice_scores={"Y": 1, "N": 0},
    use_cot=True,
)

braintrust.Eval(
    "Pedro Twelve Labs Video QA",
    data=load_data_subset,
    task=video_qa,
    scores=[evaluator],
    metadata={"model": "pegasus1.2"},
    experiment_name="pedrommvu_eval",
)